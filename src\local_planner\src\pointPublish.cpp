#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <ros/ros.h>
#include <geometry_msgs/PoseStamped.h>
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>
#include <local_planner/NavigationResult.h>
#include <local_planner/NavigationTarget.h>

using namespace std;

int point_id, point_info, gait, speed, manner, obsmode, navmode;
// ros::Publisher *pubResultPointer = NULL;
//ros::Publisher pubResult;
void goalHandler(const geometry_msgs::PoseStamped::ConstPtr& goal)//终点位姿信息以及导航参数回调函数
{
  double roll, pitch, yaw;
  geometry_msgs::Quaternion geoQuat = goal->pose.orientation;
  tf::Matrix3x3(tf::Quaternion(geoQuat.x, geoQuat.y, geoQuat.z, geoQuat.w)).getRPY(roll, pitch, yaw);
  /*
  local_planner::NavigationTarget target_point;
  target_point.pose_x = goal->pose.position.x;
  target_point.pose_y = goal->pose.position.y;
  target_point.pose_z = goal->pose.position.z;
  target_point.yaw = yaw;
  target_point.nav_mode = 1;
  target_point.point_id = point_id;
  target_point.point_info = point_info;
  target_point.gait = gait;
  target_point.speed = speed;
  target_point.manner = manner;
  target_point.obsmode = obsmode;
  target_point.navmode = navmode;
  pubResult.publish(target_point);
  */
  // std::cout<<"***********目标点已下发***********"<<std::endl;
}

void webgoalHandler(const geometry_msgs::PoseStamped::ConstPtr& goal)//终点位姿信息以及导航参数回调函数
{
  double roll, pitch, yaw;
  geometry_msgs::Quaternion geoQuat = goal->pose.orientation;
  tf::Matrix3x3(tf::Quaternion(geoQuat.x, geoQuat.y, geoQuat.z, geoQuat.w)).getRPY(roll, pitch, yaw);
  /*
  local_planner::NavigationTarget target_point;
  target_point.pose_x = goal->pose.position.x;
  target_point.pose_y = goal->pose.position.y;
  target_point.pose_z = goal->pose.position.z;
  target_point.yaw = yaw;
  target_point.nav_mode = 1;
  target_point.point_id = point_id;
  target_point.point_info = point_info;
  target_point.gait = gait;
  target_point.speed = speed;
  target_point.manner = manner;
  target_point.obsmode = obsmode;
  target_point.navmode = navmode;
  pubResult.publish(target_point);
  */
  // std::cout<<"***********web目标点已下发***********"<<std::endl;
}

int main(int argc, char** argv)
{
  ros::init(argc, argv, "pointPublish");
  ros::NodeHandle nh;
  ros::NodeHandle private_nh("~");
  private_nh.param("point_id", point_id, 0);
  private_nh.param("point_info", point_info, 0);
  private_nh.param("gait", gait, 0);
  private_nh.param("speed", speed, 1);
  private_nh.param("manner", manner, 0);
  private_nh.param("obsmode", obsmode, 1);
  private_nh.param("navmode", navmode, 0);
  //订阅导航目标点信息
  ros::Subscriber subGoal = nh.subscribe<geometry_msgs::PoseStamped> ("/move_base_simple/goal", 1, goalHandler);

  ros::Subscriber subwebGoal = nh.subscribe<geometry_msgs::PoseStamped> ("web_goal_pose", 1, webgoalHandler);
  //发布导航状态信息
  //pubResult = nh.advertise<local_planner::NavigationTarget> ("/navigation_target", 1);
  // pubResultPointer = &pubResult;
  ros::Rate rate(100);
  bool status = ros::ok();
  while (status) 
  {
    ros::spinOnce();
    rate.sleep();
    status = ros::ok();
  }
  return 0;
}