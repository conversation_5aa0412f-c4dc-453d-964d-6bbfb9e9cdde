// C的头文件
#include <iostream>
#include <stdio.h>
#include <math.h>
#include <unistd.h>
#include <time.h>
#include <fstream> //c头文件(新加)
// ros的头文件
#include <ros/ros.h>
#include <std_msgs/String.h>
#include <tf/transform_broadcaster.h>
#include <ros/time.h>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_ros/point_cloud.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/range_image/range_image.h>
#include <pcl/filters/filter.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/common.h>
#include <pcl/registration/icp.h>

#include <Eigen/Dense>

#include <sensor_msgs/PointCloud2.h>
#include <sensor_msgs/Imu.h>
#include <stdint.h>
#include <vector>
#include <ros/node_handle.h>
#include <geometry_msgs/Pose2D.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Quaternion.h>
#include <sensor_msgs/LaserScan.h>
#include <nav_msgs/Odometry.h>
#include <nav_msgs/Path.h>
#include <global_traj_generate/NavigationResult.h>
#include <global_traj_generate/NavigationTarget.h>

using namespace std;
typedef pcl::PointXYZ PointType;
#define PI 3.14159265

ros::Subscriber subLaserOdometry;
ros::Subscriber subgoalPose;
ros::Subscriber subwebgoalPose;
ros::Subscriber subGlobalPath;
ros::Publisher pubLocalGoal;

geometry_msgs::PoseStamped localGoal;		//全局系下的局部目标点
geometry_msgs::Point posenow;				//终点位置信息
pcl::PointCloud<PointType>::Ptr cloudKeyPoses3DToMap;
pcl::KdTreeFLANN<PointType>::Ptr kdtreeCloudKeyPoses3DToMap;
std::vector<int> pointSearchInd;
std::vector<float> pointSearchSqDis;
nav_msgs::Odometry odom;					//载体位置信息


class trajPoint
{
public:
	float x;
	float y;
	float z;
	float yaw;

	trajPoint(double _x, double _y, double _z, double _yaw) : x(_x), y(_y), z(_z), yaw(_yaw) {}
};
vector<trajPoint> traj;

int trajSize;								//全局轨迹航迹点数目
bool localGoalInitialFlag;					//第一个局部目标点是否初始化
int indexIncrement = 20;							//向前预移动的航迹点索引增量
int indexNum = 10;								//向前预移动的点增量
int nearestTrajPointIndexToCurrentRobotPos; //距离当前机器人位置最近的航迹点索引
int nextTrajPointIndexToCurrentRobotPos;	//距离当前机器人位置最近的航迹点的下一个航迹点索引
int localGoalIndex;							//局部目标点在全局轨迹向量中的索引
bool start_flag=false;						//是否接收到新的轨迹
double poseyaw;								//目标点航向角

void generateLocalGoal(const nav_msgs::OdometryConstPtr &msg)//根据当前位置生成局部目标点
{
	//若需坐标系转换，修改这里
	PointType pointTmp;
	pointTmp.x = msg->pose.pose.position.x;
	pointTmp.y = msg->pose.pose.position.y;
	pointTmp.z = msg->pose.pose.position.z;
	//局部目标点生成
	if (start_flag)
	{
		if (!localGoalInitialFlag)//如果第一个局部目标点未初始化，就在全局轨迹中搜索距离当前位置最邻近的轨迹点
		{
			kdtreeCloudKeyPoses3DToMap->nearestKSearch(pointTmp, 1, pointSearchInd, pointSearchSqDis);//在全局轨迹向量中搜索距离当前位置最近的轨迹点
			nearestTrajPointIndexToCurrentRobotPos = pointSearchInd[0];
			localGoalInitialFlag = true;
		}
		else
		{
			nextTrajPointIndexToCurrentRobotPos = nearestTrajPointIndexToCurrentRobotPos + indexNum;//将之后第indexNum个航迹点作为下一个位置航迹点
			if (nextTrajPointIndexToCurrentRobotPos > trajSize - 1)//限制超出轨迹总长度
			{
				nextTrajPointIndexToCurrentRobotPos = trajSize - 1;
			}
			//当前机器人位置到当前航迹点距离
			double disCurrentRobotPosToCurrentTrajPoint = std::sqrt(std::pow(pointTmp.x - cloudKeyPoses3DToMap->points[nearestTrajPointIndexToCurrentRobotPos].x, 2) +
																std::pow(pointTmp.y - cloudKeyPoses3DToMap->points[nearestTrajPointIndexToCurrentRobotPos].y, 2));
			//当前机器人位置到下一个航迹点距离
			double disCurrentRobotPosToNextTrajPoint = std::sqrt(std::pow(pointTmp.x - cloudKeyPoses3DToMap->points[nextTrajPointIndexToCurrentRobotPos].x, 2) +
															 	std::pow(pointTmp.y - cloudKeyPoses3DToMap->points[nextTrajPointIndexToCurrentRobotPos].y, 2));
			//如果距离下一个航迹点更近，就把下一个航迹点设置为当前航迹点
			if (disCurrentRobotPosToNextTrajPoint < disCurrentRobotPosToCurrentTrajPoint)
			{
				nearestTrajPointIndexToCurrentRobotPos = nextTrajPointIndexToCurrentRobotPos;
			}
		}
		//当前航迹点向后预移动一段距离（加几个索引），作为局部目标点
		localGoalIndex = nearestTrajPointIndexToCurrentRobotPos + indexIncrement;
		//索引不能超过航迹终点的索引
		if (localGoalIndex > trajSize - 1)
		{
			localGoalIndex = trajSize - 1;
			start_flag = false;//该段轨迹引导完成
		}
		//发布全局系下的局部目标点
		localGoal.pose.position.x = traj[localGoalIndex].x;
		localGoal.pose.position.y = traj[localGoalIndex].y;
		localGoal.pose.position.z = traj[localGoalIndex].z;
		localGoal.pose.orientation = tf::createQuaternionMsgFromYaw(traj[localGoalIndex].yaw);
		pubLocalGoal.publish(localGoal);	
	}
}

void generateGlobalGoal(const nav_msgs::Path::ConstPtr &msg)//读取全局轨迹并存入kd树中
{
	//初始化点云和kd树
	localGoalInitialFlag = false;
	cloudKeyPoses3DToMap.reset(new pcl::PointCloud<PointType>());
	kdtreeCloudKeyPoses3DToMap.reset(new pcl::KdTreeFLANN<PointType>());
	traj.clear();
	//读取全局轨迹长度
	size_t cloudSize = msg->poses.size();
	trajSize = (int)cloudSize;
	PointType pointTmp;
	//判断全局轨迹正负向
	if(!msg->poses.empty())
	{
		if (pow(posenow.x-msg->poses[0].pose.position.x,2)+pow(posenow.y-msg->poses[0].pose.position.y,2) >= pow(posenow.x-msg->poses[cloudSize-1].pose.position.x,2)+pow(posenow.y-msg->poses[cloudSize-1].pose.position.y,2))
		{
			for (size_t i = 0; i < cloudSize; ++i)
			{
				pointTmp.x = msg->poses[i].pose.position.x;
				pointTmp.y = msg->poses[i].pose.position.y;
				pointTmp.z = msg->poses[i].pose.position.z;
				cloudKeyPoses3DToMap->points.push_back(pointTmp);
			}
		}
		else
		{
			for (size_t i = 0; i < cloudSize; ++i)
			{
				pointTmp.x = msg->poses[cloudSize-1-i].pose.position.x;
				pointTmp.y = msg->poses[cloudSize-1-i].pose.position.y;
				pointTmp.z = msg->poses[cloudSize-1-i].pose.position.z;
				cloudKeyPoses3DToMap->points.push_back(pointTmp);
			}
		}

	//将非空全局轨迹存入kd树中
		if(!cloudKeyPoses3DToMap->empty())
			kdtreeCloudKeyPoses3DToMap->setInputCloud(cloudKeyPoses3DToMap);
	//打印结果
	//ROS_INFO("Set kdtreeCloudKeyPoses3DToMap success!");
	//根据关键帧计算航迹点
		trajPoint trajPointTmp1(cloudKeyPoses3DToMap->points[0].x, cloudKeyPoses3DToMap->points[0].y, cloudKeyPoses3DToMap->points[0].z, 0);
		traj.push_back(trajPointTmp1);
		for (size_t i = 1; i < cloudSize - 1; ++i)
		{
			//航向角为当前航迹点指向下一航迹点的方向
			float yawTmp = std::atan2(cloudKeyPoses3DToMap->points[i].y - cloudKeyPoses3DToMap->points[i - 1].y, cloudKeyPoses3DToMap->points[i].x - cloudKeyPoses3DToMap->points[i - 1].x);
			trajPoint trajPointTmp2(cloudKeyPoses3DToMap->points[i].x, cloudKeyPoses3DToMap->points[i].y, cloudKeyPoses3DToMap->points[i].z, yawTmp);
			traj.push_back(trajPointTmp2);
		}
		trajPoint trajPointTmp3(cloudKeyPoses3DToMap->points[cloudSize - 1].x, cloudKeyPoses3DToMap->points[cloudSize - 1].y, cloudKeyPoses3DToMap->points[cloudSize - 1].z, poseyaw);
		traj.push_back(trajPointTmp3);
		traj[0].yaw = traj[1].yaw;
		//新的轨迹成功读取
		start_flag = true;
	}
		//cout

}

void GoalPoseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg)//读取终点信息
{
	posenow.x = msg->pose.position.x;
	posenow.y = msg->pose.position.y;
	//posenow.z = msg->pose_z;
	//四元数转欧拉角
    tf::Quaternion quat;
    tf::quaternionMsgToTF(msg->pose.orientation, quat);
    double roll, pitch, yaw;//定义存储r\p\y的容器
    tf::Matrix3x3(quat).getRPY(roll, pitch, yaw);//进行转换
	poseyaw = yaw;
}

void WebGoalPoseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg)//读取终点信息
{
	posenow.x = msg->pose.position.x ;
    posenow.y = msg->pose.position.y ;
        //goal.pose.position.z = target->pose.position.z;
        //goal.pose.orientation = tf::createQuaternionMsgFromYaw(target->yaw);
	poseyaw = 0; //是不是要改成 msg->pose.orientation.z * M_PI / 180.0;
//	poseyaw = msg->pose.orientation.z * M_PI / 180.0;
}

int main(int argc, char **argv)
{
	ros::init(argc, argv, "global_traj_generate");
	ros::NodeHandle nh;
 	ros::NodeHandle private_nh("~");

    private_nh.param("indexIncrement", indexIncrement, 20);
    private_nh.param("indexNum", indexNum, 10);

	//订阅当前位置，根据当前位置生成局部目标点并发布
	subLaserOdometry = nh.subscribe<nav_msgs::Odometry>("/Odometry", 15, generateLocalGoal);
	//订阅终点位置以及航向角
	//subgoalPose=nh.subscribe<global_traj_generate::NavigationTarget>("/navigation_target", 1, GoalPoseCallback);
	subgoalPose=nh.subscribe<geometry_msgs::PoseStamped>("/move_base_simple/goal", 1, GoalPoseCallback);

	//Web端发布的目标点
	subwebgoalPose=nh.subscribe<geometry_msgs::PoseStamped>("/web_goal_pose", 1, WebGoalPoseCallback);

	//订阅全局轨迹，根据全局轨迹计算关键航迹点
	subGlobalPath = nh.subscribe<nav_msgs::Path>("/rrt_star_planner/RRTstarPlannerROS/plan", 15, generateGlobalGoal);
	//发布局部目标点
	pubLocalGoal = nh.advertise<geometry_msgs::PoseStamped>("/local_goal", 1);
	localGoal.header.frame_id = "map";
	
	ros::Rate rate(100);
	while (ros::ok())
	{
		ros::spinOnce();
		rate.sleep();
	}
	return 0;
}
