<?xml version="1.0"?>

<launch>

  <node pkg="local_planner" type="localPlanner" name="localPlanner" output="screen" required="true">
    <param name="pathFolder" type="string" value="$(find local_planner)/paths" />
    <rosparam command="load" file="$(find local_planner)/config/local_planner.yaml" />
  </node>

  <!-- <node pkg="local_planner" type="obstaclestop" name="obstaclestop" output="screen" required="true">
    <rosparam command="load" file="$(find local_planner)/config/obstacle_stop.yaml" />
  </node> -->

  <node pkg="local_planner" type="pathFollower" name="pathFollower" output="screen" required="true">
    <rosparam command="load" file="$(find local_planner)/config/path_follower.yaml" />
  </node>

  <node pkg="local_planner" type="calibration" name="calibration" output="screen" required="true">
    <rosparam command="load" file="$(find local_planner)/config/calibration.yaml" />
  </node>
  
  <node pkg="local_planner" type="pointPublish" name="pointPublish" output="screen" required="true">
    <rosparam command="load" file="$(find local_planner)/config/point_publish.yaml" />
  </node>

  <node pkg="tf" type="static_transform_publisher" name="vehicleTransPublisher" args="-0 -0 0 0 0 0 /sensor /vehicle 1000"/>

  <node pkg="tf" type="static_transform_publisher" name="sensorTransPublisher" args="0 0 0 -1.5707963 0 -1.5707963 /sensor /camera 1000"/>

</launch>
