#!/usr/bin/env python
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import sys

def analyze_velocity_data(csv_file):
    # 读取CSV文件
    data = pd.read_csv(csv_file)
    
    # 计算基本统计信息
    cmd_linear_mean = data['Cmd_Linear_X'].mean()
    actual_linear_mean = data['Actual_Linear_X'].mean()
    cmd_angular_mean = data['Cmd_Angular_Z'].mean()
    actual_angular_mean = data['Actual_Angular_Z'].mean()
    
    linear_error = data['Actual_Linear_X'] - data['Cmd_Linear_X']
    angular_error = data['Actual_Angular_Z'] - data['Cmd_Angular_Z']
    
    linear_error_mean = linear_error.mean()
    linear_error_std = linear_error.std()
    angular_error_mean = angular_error.mean()
    angular_error_std = angular_error.std()
    
    # 计算响应时间（假设达到命令值的90%为响应完成）
    cmd_linear_final = data['Cmd_Linear_X'].iloc[-100:].mean()  # 使用最后100个点的平均值
    cmd_angular_final = data['Cmd_Angular_Z'].iloc[-100:].mean()
    
    if abs(cmd_linear_final) > 0.01:
        threshold = 0.9 * cmd_linear_final
        for i, value in enumerate(data['Actual_Linear_X']):
            if abs(value) >= abs(threshold):
                linear_response_time = data['Time'][i]
                break
        else:
            linear_response_time = None
    else:
        linear_response_time = None
    
    if abs(cmd_angular_final) > 0.01:
        threshold = 0.9 * cmd_angular_final
        for i, value in enumerate(data['Actual_Angular_Z']):
            if abs(value) >= abs(threshold):
                angular_response_time = data['Time'][i]
                break
        else:
            angular_response_time = None
    else:
        angular_response_time = None
    
    # 打印分析结果
    print("\n===== 速度控制分析结果 =====")
    print(f"数据文件: {csv_file}")
    print(f"记录时长: {data['Time'].max():.2f} 秒")
    print("\n--- 线速度分析 ---")
    print(f"命令线速度平均值: {cmd_linear_mean:.4f} m/s")
    print(f"实际线速度平均值: {actual_linear_mean:.4f} m/s")
    print(f"线速度误差平均值: {linear_error_mean:.4f} m/s")
    print(f"线速度误差标准差: {linear_error_std:.4f} m/s")
    if linear_response_time:
        print(f"线速度响应时间 (90%): {linear_response_time:.2f} 秒")
    else:
        print("线速度响应时间: 无法计算")
    
    print("\n--- 角速度分析 ---")
    print(f"命令角速度平均值: {cmd_angular_mean:.4f} rad/s")
    print(f"实际角速度平均值: {actual_angular_mean:.4f} rad/s")
    print(f"角速度误差平均值: {angular_error_mean:.4f} rad/s")
    print(f"角速度误差标准差: {angular_error_std:.4f} rad/s")
    if angular_response_time:
        print(f"角速度响应时间 (90%): {angular_response_time:.2f} 秒")
    else:
        print("角速度响应时间: 无法计算")
    
    # 绘制图表
    plt.figure(figsize=(12, 10))
    
    # 线速度图
    plt.subplot(2, 2, 1)
    plt.plot(data['Time'], data['Cmd_Linear_X'], 'b-', label='命令线速度')
    plt.plot(data['Time'], data['Actual_Linear_X'], 'r-', label='实际线速度')
    plt.xlabel('时间 (秒)')
    plt.ylabel('线速度 (m/s)')
    plt.title('线速度响应')
    plt.grid(True)
    plt.legend()
    
    # 角速度图
    plt.subplot(2, 2, 2)
    plt.plot(data['Time'], data['Cmd_Angular_Z'], 'b-', label='命令角速度')
    plt.plot(data['Time'], data['Actual_Angular_Z'], 'r-', label='实际角速度')
    plt.xlabel('时间 (秒)')
    plt.ylabel('角速度 (rad/s)')
    plt.title('角速度响应')
    plt.grid(True)
    plt.legend()
    
    # 线速度误差图
    plt.subplot(2, 2, 3)
    plt.plot(data['Time'], linear_error, 'g-')
    plt.xlabel('时间 (秒)')
    plt.ylabel('误差 (m/s)')
    plt.title('线速度误差')
    plt.grid(True)
    
    # 角速度误差图
    plt.subplot(2, 2, 4)
    plt.plot(data['Time'], angular_error, 'g-')
    plt.xlabel('时间 (秒)')
    plt.ylabel('误差 (rad/s)')
    plt.title('角速度误差')
    plt.grid(True)
    
    plt.tight_layout()
    
    # 保存图表
    output_file = csv_file.replace('.csv', '_analysis.png')
    plt.savefig(output_file)
    print(f"\n分析图表已保存到: {output_file}")
    
    # 显示图表
    plt.show()

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("用法: ./analyze_velocity_data.py <csv_file>")
        sys.exit(1)
    
    csv_file = sys.argv[1]
    analyze_velocity_data(csv_file)