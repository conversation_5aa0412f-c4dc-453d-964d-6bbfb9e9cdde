#!/usr/bin/env python
import rospy
from geometry_msgs.msg import Twist
import time
import sys

def send_constant_velocity(linear_x, angular_z, duration, rate_hz=10):
    """
    发送恒定速度命令
    
    参数:
        linear_x: 线速度 (m/s)
        angular_z: 角速度 (rad/s)
        duration: 持续时间 (秒)
        rate_hz: 发布频率 (Hz)
    """
    # 初始化ROS节点
    rospy.init_node('constant_velocity_test', anonymous=True)
    
    # 创建速度命令发布者
    pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
    
    # 设置发布频率
    rate = rospy.Rate(rate_hz)
    
    # 创建Twist消息
    twist = Twist()
    twist.linear.x = linear_x
    twist.linear.y = 0.0
    twist.linear.z = 0.0
    twist.angular.x = 0.0
    twist.angular.y = 0.0
    twist.angular.z = angular_z
    
    # 记录开始时间
    start_time = time.time()
    
    print(f"发送恒定速度命令: 线速度={linear_x} m/s, 角速度={angular_z} rad/s")
    print(f"持续时间: {duration} 秒")
    
    # 发送速度命令直到达到指定持续时间
    while time.time() - start_time < duration and not rospy.is_shutdown():
        pub.publish(twist)
        rate.sleep()
    
    # 发送停止命令
    twist.linear.x = 0.0
    twist.angular.z = 0.0
    pub.publish(twist)
    print("测试完成，已发送停止命令")

if __name__ == '__main__':
    try:
        # 从命令行参数获取速度值和持续时间
        if len(sys.argv) >= 4:
            linear_x = float(sys.argv[1])
            angular_z = float(sys.argv[2])
            duration = float(sys.argv[3])
        else:
            linear_x = 0.2  # 默认线速度 0.2 m/s
            angular_z = 0.0  # 默认角速度 0.0 rad/s
            duration = 10.0  # 默认持续时间 10 秒
        
        send_constant_velocity(linear_x, angular_z, duration)
    except rospy.ROSInterruptException:
        pass