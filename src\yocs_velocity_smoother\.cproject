<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="cdt.managedbuild.toolchain.gnu.base.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="cdt.managedbuild.toolchain.gnu.base.**********" moduleId="org.eclipse.cdt.core.settings" name="Default">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration buildProperties="" description="" id="cdt.managedbuild.toolchain.gnu.base.**********" name="Default" parent="org.eclipse.cdt.build.core.emptycfg">
					<folderInfo id="cdt.managedbuild.toolchain.gnu.base.**********.**********" name="/" resourcePath="">
						<toolChain id="cdt.managedbuild.toolchain.gnu.base.55488484" name="cdt.managedbuild.toolchain.gnu.base" superClass="cdt.managedbuild.toolchain.gnu.base">
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="cdt.managedbuild.target.gnu.platform.base.112613808" name="Debug Platform" osList="linux,hpux,aix,qnx" superClass="cdt.managedbuild.target.gnu.platform.base"/>
							<builder arguments="-f ${YUJIN_WORKSPACE}/build/yujin_ocs/${ProjName}/Makefile -j8" command="make" id="cdt.managedbuild.target.gnu.builder.base.1084376902" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" superClass="cdt.managedbuild.target.gnu.builder.base"/>
							<tool id="cdt.managedbuild.tool.gnu.archiver.base.1005331362" name="GCC Archiver" superClass="cdt.managedbuild.tool.gnu.archiver.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.compiler.base.743265677" name="GCC C++ Compiler" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.base">
								<inputType id="cdt.managedbuild.tool.gnu.cpp.compiler.input.1137828093" superClass="cdt.managedbuild.tool.gnu.cpp.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.compiler.base.**********" name="GCC C Compiler" superClass="cdt.managedbuild.tool.gnu.c.compiler.base">
								<inputType id="cdt.managedbuild.tool.gnu.c.compiler.input.**********" superClass="cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.c.linker.base.2129912222" name="GCC C Linker" superClass="cdt.managedbuild.tool.gnu.c.linker.base"/>
							<tool id="cdt.managedbuild.tool.gnu.cpp.linker.base.1355954279" name="GCC C++ Linker" superClass="cdt.managedbuild.tool.gnu.cpp.linker.base">
								<inputType id="cdt.managedbuild.tool.gnu.cpp.linker.input.305096953" superClass="cdt.managedbuild.tool.gnu.cpp.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="cdt.managedbuild.tool.gnu.assembler.base.1032860904" name="GCC Assembler" superClass="cdt.managedbuild.tool.gnu.assembler.base">
								<inputType id="cdt.managedbuild.tool.gnu.assembler.input.1386726648" superClass="cdt.managedbuild.tool.gnu.assembler.input"/>
							</tool>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name="src"/>
						<entry flags="VALUE_WORKSPACE_PATH" kind="sourcePath" name="include/yocs_velocity_smoother"/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="yocs_velocity_smoother.null.1145713650" name="yocs_velocity_smoother"/>
	</storageModule>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Default">
			<resource resourceType="PROJECT" workspacePath="/yocs_velocity_smoother"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********;cdt.managedbuild.toolchain.gnu.base.**********.**********;cdt.managedbuild.tool.gnu.cpp.compiler.base.743265677;cdt.managedbuild.tool.gnu.cpp.compiler.input.1137828093">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileCPP"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="cdt.managedbuild.toolchain.gnu.base.**********;cdt.managedbuild.toolchain.gnu.base.**********.**********;cdt.managedbuild.tool.gnu.c.compiler.base.**********;cdt.managedbuild.tool.gnu.c.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="org.eclipse.cdt.managedbuilder.core.GCCManagedMakePerProjectProfileC"/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
</cproject>
