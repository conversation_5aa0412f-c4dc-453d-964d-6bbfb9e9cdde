#!/usr/bin/env python
import rospy
from geometry_msgs.msg import Twist, PoseStamped
from nav_msgs.msg import Odometry, Path
import tf
import math
import time
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle

class SquarePathTest:
    def __init__(self):
        # 初始化ROS节点
        rospy.init_node('square_path_test', anonymous=True)
        
        # 参数设置
        self.linear_speed = rospy.get_param('~linear_speed', 0.2)  # 线速度 (m/s)
        self.side_length = rospy.get_param('~side_length', 1.0)    # 正方形边长 (m)
        self.turn_speed = rospy.get_param('~turn_speed', 0.5)      # 转弯角速度 (rad/s)
        self.position_tolerance = rospy.get_param('~position_tolerance', 0.05)  # 位置容差 (m)
        self.angle_tolerance = rospy.get_param('~angle_tolerance', 0.1)         # 角度容差 (rad)
        
        # 创建发布者和订阅者
        self.cmd_vel_pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
        self.path_pub = rospy.Publisher('/ideal_path', Path, queue_size=1, latch=True)
        self.odom_sub = rospy.Subscriber('/odom', Odometry, self.odom_callback)
        
        # 初始化变量
        self.current_pose = None
        self.start_pose = None
        self.recorded_path = []
        self.ideal_path = []
        self.is_running = False
        self.start_time = None
        
        # 等待里程计数据
        rospy.loginfo("等待里程计数据...")
        while self.current_pose is None and not rospy.is_shutdown():
            rospy.sleep(0.1)
        
        rospy.loginfo("准备开始路径跟踪测试")
    
    def odom_callback(self, msg):
        # 从里程计消息中提取位姿信息
        position = msg.pose.pose.position
        orientation = msg.pose.pose.orientation
        
        # 转换四元数为欧拉角
        quaternion = (
            orientation.x,
            orientation.y,
            orientation.z,
            orientation.w
        )
        _, _, yaw = tf.transformations.euler_from_quaternion(quaternion)
        
        # 更新当前位姿
        self.current_pose = {
            'x': position.x,
            'y': position.y,
            'yaw': yaw
        }
        
        # 记录路径点
        if self.is_running and self.start_pose is not None:
            # 计算相对于起始位置的坐标
            rel_x = position.x - self.start_pose['x']
            rel_y = position.y - self.start_pose['y']
            
            # 记录位置和时间
            self.recorded_path.append({
                'x': rel_x,
                'y': rel_y,
                'yaw': yaw,
                'time': rospy.Time.now().to_sec() - self.start_time
            })
    
    def generate_ideal_path(self):
        """生成理想的正方形路径"""
        path = Path()
        path.header.frame_id = "odom"
        path.header.stamp = rospy.Time.now()
        
        # 正方形的四个顶点 (相对坐标)
        corners = [
            (0, 0),
            (self.side_length, 0),
            (self.side_length, self.side_length),
            (0, self.side_length),
            (0, 0)  # 回到起点
        ]
        
        # 为每个边生成路径点
        points_per_side = 50
        self.ideal_path = []
        
        for i in range(len(corners) - 1):
            x1, y1 = corners[i]
            x2, y2 = corners[i+1]
            
            for j in range(points_per_side):
                ratio = float(j) / points_per_side
                x = x1 + ratio * (x2 - x1)
                y = y1 + ratio * (y2 - y1)
                
                # 添加到理想路径列表
                self.ideal_path.append({'x': x, 'y': y})
                
                # 创建路径点消息
                pose = PoseStamped()
                pose.header = path.header
                pose.pose.position.x = x
                pose.pose.position.y = y
                pose.pose.position.z = 0
                
                # 计算方向 (沿着路径方向)
                if x2 != x1 or y2 != y1:
                    yaw = math.atan2(y2 - y1, x2 - x1)
                else:
                    yaw = 0
                
                quaternion = tf.transformations.quaternion_from_euler(0, 0, yaw)
                pose.pose.orientation.x = quaternion[0]
                pose.pose.orientation.y = quaternion[1]
                pose.pose.orientation.z = quaternion[2]
                pose.pose.orientation.w = quaternion[3]
                
                path.poses.append(pose)
        
        # 发布理想路径
        self.path_pub.publish(path)
        rospy.loginfo("已生成并发布理想路径")
    
    def move_straight(self, distance):
        """直线运动指定距离"""
        rospy.loginfo(f"直线运动 {distance} 米")
        
        # 记录起始位置
        start_position = {
            'x': self.current_pose['x'],
            'y': self.current_pose['y']
        }
        
        # 计算目标位置
        target_yaw = self.current_pose['yaw']
        target_x = start_position['x'] + distance * math.cos(target_yaw)
        target_y = start_position['y'] + distance * math.sin(target_yaw)
        
        # 创建速度命令
        twist = Twist()
        twist.linear.x = self.linear_speed
        
        # 设置控制频率
        rate = rospy.Rate(10)  # 10 Hz
        
        # 运动直到达到目标位置
        while not rospy.is_shutdown():
            # 计算当前位置到目标位置的距离
            current_x = self.current_pose['x']
            current_y = self.current_pose['y']
            remaining_distance = math.sqrt((target_x - current_x)**2 + (target_y - current_y)**2)
            
            # 如果达到目标位置，则停止
            if remaining_distance < self.position_tolerance:
                break
            
            # 发布速度命令
            self.cmd_vel_pub.publish(twist)
            rate.sleep()
        
        # 停止机器人
        self.stop_robot()
        rospy.loginfo("直线运动完成")
    
    def turn(self, angle):
        """原地旋转指定角度 (弧度)"""
        rospy.loginfo(f"旋转 {angle} 弧度")
        
        # 计算目标朝向
        target_yaw = self.normalize_angle(self.current_pose['yaw'] + angle)
        
        # 确定旋转方向
        if angle > 0:
            twist = Twist()
            twist.angular.z = self.turn_speed
        else:
            twist = Twist()
            twist.angular.z = -self.turn_speed
        
        # 设置控制频率
        rate = rospy.Rate(10)  # 10 Hz
        
        # 旋转直到达到目标朝向
        while not rospy.is_shutdown():
            # 计算当前朝向与目标朝向的差异
            yaw_diff = self.normalize_angle(target_yaw - self.current_pose['yaw'])
            
            # 如果达到目标朝向，则停止
            if abs(yaw_diff) < self.angle_tolerance:
                break
            
            # 发布速度命令
            self.cmd_vel_pub.publish(twist)
            rate.sleep()
        
        # 停止机器人
        self.stop_robot()
        rospy.loginfo("旋转完成")
    
    def stop_robot(self):
        """停止机器人"""
        twist = Twist()
        self.cmd_vel_pub.publish(twist)
        rospy.sleep(0.5)  # 等待机器人完全停止
    
    def normalize_angle(self, angle):
        """将角度归一化到 [-pi, pi] 范围内"""
        while angle > math.pi:
            angle -= 2 * math.pi
        while angle < -math.pi:
            angle += 2 * math.pi
        return angle
    
    def run_square_path(self):
        """执行正方形路径测试"""
        rospy.loginfo("开始正方形路径测试")
        
        # 记录起始位姿
        self.start_pose = self.current_pose.copy()
        self.is_running = True
        self.start_time = rospy.Time.now().to_sec()
        
        # 生成理想路径
        self.generate_ideal_path()
        
        # 执行正方形路径
        for i in range(4):  # 四条边
            # 直线运动
            self.move_straight(self.side_length)
            
            # 左转90度 (π/2弧度)
            self.turn(math.pi/2)
        
        self.is_running = False
        rospy.loginfo("正方形路径测试完成")
    
    def calculate_path_error(self):
        """计算实际路径与理想路径的误差"""
        if not self.recorded_path or not self.ideal_path:
            rospy.logwarn("没有足够的路径数据进行误差计算")
            return None
        
        # 重采样理想路径，使其点数与记录的路径相同
        ideal_resampled = []
        recorded_len = len(self.recorded_path)
        ideal_len = len(self.ideal_path)
        
        for i in range(recorded_len):
            idx = min(int(i * ideal_len / recorded_len), ideal_len - 1)
            ideal_resampled.append(self.ideal_path[idx])
        
        # 计算每个点的误差
        errors = []
        for i in range(min(len(ideal_resampled), len(self.recorded_path))):
            ideal = ideal_resampled[i]
            actual = self.recorded_path[i]
            
            # 计算欧几里得距离
            error = math.sqrt((ideal['x'] - actual['x'])**2 + (ideal['y'] - actual['y'])**2)
            errors.append(error)
        
        # 计算统计数据
        mean_error = np.mean(errors)
        max_error = np.max(errors)
        std_error = np.std(errors)
        
        return {
            'mean': mean_error,
            'max': max_error,
            'std': std_error,
            'errors': errors
        }
    
    def plot_results(self):
        """绘制测试结果"""
        if not self.recorded_path or not self.ideal_path:
            rospy.logwarn("没有足够的数据进行绘图")
            return
        
        # 提取路径数据
        ideal_x = [p['x'] for p in self.ideal_path]
        ideal_y = [p['y'] for p in self.ideal_path]
        
        recorded_x = [p['x'] for p in self.recorded_path]
        recorded_y = [p['y'] for p in self.recorded_path]
        recorded_time = [p['time'] for p in self.recorded_path]
        
        # 计算路径误差
        error_data = self.calculate_path_error()
        
        # 创建图形
        plt.figure(figsize=(15, 10))
        
        # 绘制路径对比图
        plt.subplot(2, 2, 1)
        plt.plot(ideal_x, ideal_y, 'b-', linewidth=2, label='理想路径')
        plt.plot(recorded_x, recorded_y, 'r-', linewidth=1, label='实际路径')
        plt.scatter(recorded_x[0], recorded_y[0], color='g', s=100, marker='o', label='起点')
        plt.scatter(recorded_x[-1], recorded_y[-1], color='r', s=100, marker='x', label='终点')
        
        # 添加正方形参考
        plt.gca().add_patch(Rectangle((0, 0), self.side_length, self.side_length, 
                                     fill=False, edgecolor='g', linestyle='--'))
        
        plt.xlabel('X (米)')
        plt.ylabel('Y (米)')
        plt.title('路径对比')
        plt.grid(True)
        plt.axis('equal')
        plt.legend()
        
        # 绘制X坐标随时间变化图
        plt.subplot(2, 2, 2)
        plt.plot(recorded_time, recorded_x, 'b-')
        plt.xlabel('时间 (秒)')
        plt.ylabel('X 坐标 (米)')
        plt.title('X 坐标随时间变化')
        plt.grid(True)
        
        # 绘制Y坐标随时间变化图
        plt.subplot(2, 2, 3)
        plt.plot(recorded_time, recorded_y, 'r-')
        plt.xlabel('时间 (秒)')
        plt.ylabel('Y 坐标 (米)')
        plt.title('Y 坐标随时间变化')
        plt.grid(True)
        
        # 绘制路径误差图
        if error_data:
            plt.subplot(2, 2, 4)
            plt.plot(recorded_time[:len(error_data['errors'])], error_data['errors'], 'g-')
            plt.axhline(y=error_data['mean'], color='r', linestyle='--', label=f'平均误差: {error_data["mean"]:.3f} m')
            plt.xlabel('时间 (秒)')
            plt.ylabel('路径误差 (米)')
            plt.title('路径跟踪误差')
            plt.grid(True)
            plt.legend()
            
            # 打印误差统计
            rospy.loginfo(f"路径误差统计:")
            rospy.loginfo(f"  平均误差: {error_data['mean']:.3f} 米")
            rospy.loginfo(f"  最大误差: {error_data['max']:.3f} 米")
            rospy.loginfo(f"  标准差: {error_data['std']:.3f} 米")
        
        plt.tight_layout()
        
        # 保存图形
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = f"square_path_test_{timestamp}.png"
        plt.savefig(filename)
        rospy.loginfo(f"结果图表已保存到: {filename}")
        
        # 显示图形
        plt.show()

def main():
    try:
        # 创建测试对象
        test = SquarePathTest()
        
        # 等待用户确认开始测试
        input("按Enter键开始测试...")
        
        # 执行测试
        test.run_square_path()
        
        # 绘制结果
        test.plot_results()
        
    except rospy.ROSInterruptException:
        pass

if __name__ == '__main__':
    main()