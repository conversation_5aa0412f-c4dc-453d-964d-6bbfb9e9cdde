#!/usr/bin/env python
import rospy
from geometry_msgs.msg import Twist
from nav_msgs.msg import Odometry
import csv
import time
import os

class VelocityLogger:
    def __init__(self, output_file):
        # 初始化ROS节点
        rospy.init_node('velocity_logger', anonymous=True)
        
        # 创建CSV文件
        self.csv_file = open(output_file, 'w')
        self.csv_writer = csv.writer(self.csv_file)
        self.csv_writer.writerow(['Time', 'Cmd_Linear_X', 'Cmd_Angular_Z', 
                                 'Actual_Linear_X', 'Actual_Angular_Z'])
        
        # 记录起始时间
        self.start_time = time.time()
        
        # 存储最新的命令和实际速度
        self.cmd_vel = Twist()
        self.actual_vel = Twist()
        
        # 创建订阅者
        self.cmd_sub = rospy.Subscriber('/cmd_vel', Twist, self.cmd_callback)
        self.odom_sub = rospy.Subscriber('/odom', Odometry, self.odom_callback)
        
        # 创建定时器，定期记录数据
        self.timer = rospy.Timer(rospy.Duration(0.1), self.timer_callback)
        
        rospy.loginfo(f"开始记录速度数据到: {output_file}")
    
    def cmd_callback(self, msg):
        self.cmd_vel = msg
    
    def odom_callback(self, msg):
        self.actual_vel.linear.x = msg.twist.twist.linear.x
        self.actual_vel.angular.z = msg.twist.twist.angular.z
    
    def timer_callback(self, event):
        # 计算经过的时间
        elapsed_time = time.time() - self.start_time
        
        # 记录数据
        self.csv_writer.writerow([
            elapsed_time,
            self.cmd_vel.linear.x,
            self.cmd_vel.angular.z,
            self.actual_vel.linear.x,
            self.actual_vel.angular.z
        ])
        self.csv_file.flush()  # 确保数据写入文件
    
    def shutdown(self):
        self.csv_file.close()
        rospy.loginfo("停止记录速度数据")

if __name__ == '__main__':
    try:
        # 创建输出文件名，包含时间戳
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        output_file = f"velocity_test_{timestamp}.csv"
        
        # 创建记录器
        logger = VelocityLogger(output_file)
        
        # 保持节点运行，直到被关闭
        rospy.spin()
        
        # 关闭记录器
        logger.shutdown()
    except rospy.ROSInterruptException:
        pass