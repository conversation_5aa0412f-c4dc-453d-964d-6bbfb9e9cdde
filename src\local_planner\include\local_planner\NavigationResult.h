// Generated by gencpp from file local_planner/NavigationResult.msg
// DO NOT EDIT!


#ifndef LOCAL_PLANNER_MESSAGE_NAVIGATIONRESULT_H
#define LOCAL_PLANNER_MESSAGE_NAVIGATIONRESULT_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace local_planner
{
template <class ContainerAllocator>
struct NavigationResult_
{
  typedef NavigationResult_<ContainerAllocator> Type;

  NavigationResult_()
    : point_id(0)
    , target_pose_x(0.0)
    , target_pose_y(0.0)
    , target_pose_z(0.0)
    , target_yaw(0.0)
    , current_pose_x(0.0)
    , current_pose_y(0.0)
    , current_pose_z(0.0)
    , current_yaw(0.0)
    , nav_state(0)  {
    }
  NavigationResult_(const ContainerAllocator& _alloc)
    : point_id(0)
    , target_pose_x(0.0)
    , target_pose_y(0.0)
    , target_pose_z(0.0)
    , target_yaw(0.0)
    , current_pose_x(0.0)
    , current_pose_y(0.0)
    , current_pose_z(0.0)
    , current_yaw(0.0)
    , nav_state(0)  {
  (void)_alloc;
    }



   typedef int32_t _point_id_type;
  _point_id_type point_id;

   typedef double _target_pose_x_type;
  _target_pose_x_type target_pose_x;

   typedef double _target_pose_y_type;
  _target_pose_y_type target_pose_y;

   typedef double _target_pose_z_type;
  _target_pose_z_type target_pose_z;

   typedef double _target_yaw_type;
  _target_yaw_type target_yaw;

   typedef double _current_pose_x_type;
  _current_pose_x_type current_pose_x;

   typedef double _current_pose_y_type;
  _current_pose_y_type current_pose_y;

   typedef double _current_pose_z_type;
  _current_pose_z_type current_pose_z;

   typedef double _current_yaw_type;
  _current_yaw_type current_yaw;

   typedef int32_t _nav_state_type;
  _nav_state_type nav_state;





  typedef boost::shared_ptr< ::local_planner::NavigationResult_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::local_planner::NavigationResult_<ContainerAllocator> const> ConstPtr;

}; // struct NavigationResult_

typedef ::local_planner::NavigationResult_<std::allocator<void> > NavigationResult;

typedef boost::shared_ptr< ::local_planner::NavigationResult > NavigationResultPtr;
typedef boost::shared_ptr< ::local_planner::NavigationResult const> NavigationResultConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::local_planner::NavigationResult_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::local_planner::NavigationResult_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::local_planner::NavigationResult_<ContainerAllocator1> & lhs, const ::local_planner::NavigationResult_<ContainerAllocator2> & rhs)
{
  return lhs.point_id == rhs.point_id &&
    lhs.target_pose_x == rhs.target_pose_x &&
    lhs.target_pose_y == rhs.target_pose_y &&
    lhs.target_pose_z == rhs.target_pose_z &&
    lhs.target_yaw == rhs.target_yaw &&
    lhs.current_pose_x == rhs.current_pose_x &&
    lhs.current_pose_y == rhs.current_pose_y &&
    lhs.current_pose_z == rhs.current_pose_z &&
    lhs.current_yaw == rhs.current_yaw &&
    lhs.nav_state == rhs.nav_state;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::local_planner::NavigationResult_<ContainerAllocator1> & lhs, const ::local_planner::NavigationResult_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace local_planner

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::local_planner::NavigationResult_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::local_planner::NavigationResult_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::local_planner::NavigationResult_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::local_planner::NavigationResult_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::local_planner::NavigationResult_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::local_planner::NavigationResult_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::local_planner::NavigationResult_<ContainerAllocator> >
{
  static const char* value()
  {
    return "646e7d35f473f7e3f920aaa6395e10e8";
  }

  static const char* value(const ::local_planner::NavigationResult_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x646e7d35f473f7e3ULL;
  static const uint64_t static_value2 = 0xf920aaa6395e10e8ULL;
};

template<class ContainerAllocator>
struct DataType< ::local_planner::NavigationResult_<ContainerAllocator> >
{
  static const char* value()
  {
    return "local_planner/NavigationResult";
  }

  static const char* value(const ::local_planner::NavigationResult_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::local_planner::NavigationResult_<ContainerAllocator> >
{
  static const char* value()
  {
    return "int32 point_id   # 点位编号\n"
"float64 target_pose_x\n"
"float64 target_pose_y\n"
"float64 target_pose_z # 预留\n"
"float64 target_yaw\n"
"\n"
"float64 current_pose_x\n"
"float64 current_pose_y\n"
"float64 current_pose_z # 预留\n"
"float64 current_yaw\n"
"\n"
"int32 nav_state # 0：到达目标点; 1：失败；\n"
"\n"
;
  }

  static const char* value(const ::local_planner::NavigationResult_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::local_planner::NavigationResult_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.point_id);
      stream.next(m.target_pose_x);
      stream.next(m.target_pose_y);
      stream.next(m.target_pose_z);
      stream.next(m.target_yaw);
      stream.next(m.current_pose_x);
      stream.next(m.current_pose_y);
      stream.next(m.current_pose_z);
      stream.next(m.current_yaw);
      stream.next(m.nav_state);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct NavigationResult_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::local_planner::NavigationResult_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::local_planner::NavigationResult_<ContainerAllocator>& v)
  {
    s << indent << "point_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.point_id);
    s << indent << "target_pose_x: ";
    Printer<double>::stream(s, indent + "  ", v.target_pose_x);
    s << indent << "target_pose_y: ";
    Printer<double>::stream(s, indent + "  ", v.target_pose_y);
    s << indent << "target_pose_z: ";
    Printer<double>::stream(s, indent + "  ", v.target_pose_z);
    s << indent << "target_yaw: ";
    Printer<double>::stream(s, indent + "  ", v.target_yaw);
    s << indent << "current_pose_x: ";
    Printer<double>::stream(s, indent + "  ", v.current_pose_x);
    s << indent << "current_pose_y: ";
    Printer<double>::stream(s, indent + "  ", v.current_pose_y);
    s << indent << "current_pose_z: ";
    Printer<double>::stream(s, indent + "  ", v.current_pose_z);
    s << indent << "current_yaw: ";
    Printer<double>::stream(s, indent + "  ", v.current_yaw);
    s << indent << "nav_state: ";
    Printer<int32_t>::stream(s, indent + "  ", v.nav_state);
  }
};

} // namespace message_operations
} // namespace ros

#endif // LOCAL_PLANNER_MESSAGE_NAVIGATIONRESULT_H
