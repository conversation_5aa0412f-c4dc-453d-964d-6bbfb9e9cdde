# 创建一个脚本，逐渐增加速度
#!/usr/bin/env python
import rospy
from geometry_msgs.msg import Twist
import time

def test_acceleration(max_speed, ramp_time, hold_time):
    rospy.init_node('acceleration_test', anonymous=True)
    pub = rospy.Publisher('/cmd_vel', Twist, queue_size=10)
    rate = rospy.Rate(20)  # 20Hz
    
    twist = Twist()
    start_time = time.time()
    
    print(f"开始加速测试: 最大速度={max_speed} m/s, 加速时间={ramp_time} 秒")
    
    while not rospy.is_shutdown():
        current_time = time.time() - start_time
        
        if current_time < ramp_time:
            # 线性增加速度
            ratio = current_time / ramp_time
            twist.linear.x = max_speed * ratio
        elif current_time < ramp_time + hold_time:
            # 保持最大速度
            twist.linear.x = max_speed
        else:
            # 测试结束
            twist.linear.x = 0.0
            pub.publish(twist)
            print("测试完成")
            break
        
        pub.publish(twist)
        rate.sleep()

if __name__ == '__main__':
    try:
        test_acceleration(0.5, 5.0, 3.0)  # 5秒内加速到0.5 m/s，然后保持3秒
    except rospy.ROSInterruptException:
        pass
