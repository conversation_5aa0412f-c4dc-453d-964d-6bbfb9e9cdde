#include <dynamic_detector/dynamic_detector.hpp>
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>
#include <image_transport/image_transport.h>
#include <cv_bridge/cv_bridge.h>
#include <pcl_conversions/pcl_conversions.h>
#include "prejection.h"


using namespace std;

std::string cloud_topic;
std::string odom_topic;
std::string terrain_cloud_topic;
double width, resolution, occupancy_threshold, log_odds_increase, log_odds_decrease;
double updatetime, vehicle_length, vehicle_width, z_max, z_min;
int beam_num;
bool using_map;
ros::Subscriber sub_cloud;
ros::Subscriber sub_odom;
ros::Subscriber sub_map;
ros::Publisher pub_map;
ros::Publisher pub_terrain_cloud;
ros::Publisher pub_odometry;
tf::StampedTransform odomTrans;
nav_msgs::Odometry::Ptr odometry(new nav_msgs::Odometry);
Eigen::Affine3d lidar_k2init;
DynamicCloudDetector Dynamic;
nav_msgs::OccupancyGrid global_map_origin;
tf::TransformBroadcaster *tfBroadcasterPointer = NULL;

Eigen::VectorXd  g_lidar2camera;  //相机在雷达坐标系下的位姿
Reprejection g_prejector;
image_transport::Publisher debug_image_pub;
ThreadSafeTimed< pcl::PointCloud<pcl::PointXYZI>::Ptr > g_depth_cloud;

//一定半径范围内均匀采样
std::vector<Eigen::Vector2d> gridSampleAroundPosition(const Eigen::Vector3d& currentPos, double radius, int pointsPerAxis) {
    std::vector<Eigen::Vector2d> samples;
    double step = 2 * radius / (pointsPerAxis - 1);
    
    for (int i = 0; i < pointsPerAxis; ++i) {
        for (int j = 0; j < pointsPerAxis; ++j) {
            Eigen::Vector2d sample;
            sample(0) = currentPos(0) - radius + i * step;
            sample(1)= currentPos(1) - radius + j * step;
            
            // Check if the sample is within the radius
            double dist = sqrt(pow(sample(0) - currentPos(0), 2) + pow(sample(1) - currentPos(1), 2));
            if (dist <= radius) {
                samples.push_back(sample);
            }
        }
    }
    
    return samples;
}


void callbackCloud(const sensor_msgs::PointCloud2::ConstPtr& cloud_msg)
{
    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud_lidar(new pcl::PointCloud<pcl::PointXYZI>());
    pcl::fromROSMsg(*cloud_msg, *cloud_lidar);

    if(!g_depth_cloud.timeout()){
        *cloud_lidar += *(g_depth_cloud.get());
    }

    pcl::PointCloud<pcl::PointXYZI>::Ptr terrain_cloud_map(new pcl::PointCloud<pcl::PointXYZI>());
    Dynamic.process(cloud_lidar, odometry, terrain_cloud_map, g_prejector);
    static int pubnum = 1;
    nav_msgs::OccupancyGrid global_map = global_map_origin;
    sensor_msgs::PointCloud2::Ptr terrain_cloud_ex(new sensor_msgs::PointCloud2);
    if (using_map)
    {
        for (int i = 0; i < int(terrain_cloud_map->points.size()); i++)
        {
            int x = int((terrain_cloud_map->points[i].x - global_map.info.origin.position.x) / global_map.info.resolution);
            int y = int((terrain_cloud_map->points[i].y - global_map.info.origin.position.y) / global_map.info.resolution);
            if (x >= global_map.info.width || x < 0)
            {
                terrain_cloud_map->points[i].intensity = 100;
                continue;
            }
            if (y >= global_map.info.height || y < 0)
            {
                terrain_cloud_map->points[i].intensity = 100;
                continue;
            }

            if (global_map.data[x + y * global_map.info.width] > 0)
            	terrain_cloud_map->points[i].intensity = 100;
           // else if (global_map.data[x + y * global_map.info.width] == 0 && terrain_cloud_map->points[i].intensity > 1)
           //   global_map.data[x + y * global_map.info.width] = 100;
        }

        // 生成周边一定范围内的静态点云
        pcl::PointCloud<pcl::PointXYZI>::Ptr unaccess_static_cloud_pcl(new pcl::PointCloud<pcl::PointXYZI>());
        const float C_DET_RADIUS = 5.0;
        Eigen::Vector3d odom_t = lidar_k2init.translation();
        const int C_POINTS_PER_AXIS = 80; 
        auto sample_points = gridSampleAroundPosition(odom_t, C_DET_RADIUS, C_POINTS_PER_AXIS);

        for (int i = 0; i < sample_points.size(); i++)
        {
            int x = int((sample_points[i](0) - global_map.info.origin.position.x) / global_map.info.resolution);
            int y = int((sample_points[i](1) - global_map.info.origin.position.y) / global_map.info.resolution);
            if (x >= global_map.info.width || x < 0)
            {
              //  unaccess_static_cloud_pcl->points[i].intensity = 100;
                continue;
            }
            if (y >= global_map.info.height || y < 0)
            {
               // unaccess_static_cloud_pcl->points[i].intensity = 100;
                continue;
            }

            if (global_map.data[x + y * global_map.info.width] > 0){
                pcl::PointXYZI point;
                point.x = sample_points[i](0);
                point.y = sample_points[i](1);
                point.intensity = 100;
                unaccess_static_cloud_pcl->points.push_back(point);
            }
        }
      //  pcl::toROSMsg(*terrain_cloud_pcl, *terrain_cloud);
        pcl::toROSMsg(*terrain_cloud_map + *unaccess_static_cloud_pcl, *terrain_cloud_ex);
    }
	else{
		pcl::toROSMsg(*terrain_cloud_map, *terrain_cloud_ex);
	}
    pubnum = pubnum - 1;
    if (pubnum == 0)
    {
        pub_map.publish(global_map);
        pubnum = 100;
    }

   // sensor_msgs::PointCloud2 terrain_msg;
    //pcl::toROSMsg(*terrain_cloud_map, terrain_msg);
    terrain_cloud_ex->header.frame_id = "map";
    terrain_cloud_ex->header.stamp = cloud_msg->header.stamp;
    pub_terrain_cloud.publish(*terrain_cloud_ex);


    cv::Mat debug_image=g_prejector.debug_img().clone();
    g_prejector.clear_debug();
    if (debug_image.channels() == 1) {
        // 单通道转BGR（灰度图复制到3个通道）
        cv::cvtColor(debug_image, debug_image, CV_GRAY2BGR);
    } else if (debug_image.channels() == 4) {
        // 4通道（BGRA）转BGR
        cv::cvtColor(debug_image, debug_image, CV_BGRA2BGR);
    }
    sensor_msgs::ImagePtr debug_msg = cv_bridge::CvImage(cloud_msg->header, "bgr8", debug_image).toImageMsg();

    debug_image_pub.publish(debug_msg);
    
}

void callbackOdom(const nav_msgs::Odometry::ConstPtr& odom_msg)
{
    *odometry = *odom_msg;
    odometry->header.frame_id = "map";
    odometry->child_frame_id = "sensor";
    pub_odometry.publish(*odometry);

    Eigen::Vector3d t;
    t << odom_msg->pose.pose.position.x, odom_msg->pose.pose.position.y, odom_msg->pose.pose.position.z;
    Eigen::Quaterniond t_Q;
    t_Q.x() = odom_msg->pose.pose.orientation.x;
    t_Q.y() = odom_msg->pose.pose.orientation.y;
    t_Q.z() = odom_msg->pose.pose.orientation.z;
    t_Q.w() = odom_msg->pose.pose.orientation.w;
    lidar_k2init = ( Eigen::Translation3d (t.cast<double>()) * Eigen::AngleAxisd ((t_Q.toRotationMatrix()).cast<double>()));  

    odomTrans.stamp_ = odometry->header.stamp;
    odomTrans.frame_id_ = "map";
    odomTrans.child_frame_id_ = "sensor";
    odomTrans.setRotation(tf::Quaternion(odometry->pose.pose.orientation.x, odometry->pose.pose.orientation.y, odometry->pose.pose.orientation.z, odometry->pose.pose.orientation.w));
    odomTrans.setOrigin(tf::Vector3(odometry->pose.pose.position.x, odometry->pose.pose.position.y, odometry->pose.pose.position.z));
    tfBroadcasterPointer->sendTransform(odomTrans);

    tf::StampedTransform odomTrans_vechle;
    odomTrans_vechle.stamp_ = odometry->header.stamp;
    odomTrans_vechle.frame_id_ = "map";
    odomTrans_vechle.child_frame_id_ = "vehicle";
    odomTrans_vechle.setRotation(tf::Quaternion(odometry->pose.pose.orientation.x, odometry->pose.pose.orientation.y, odometry->pose.pose.orientation.z, odometry->pose.pose.orientation.w));
    odomTrans_vechle.setOrigin(tf::Vector3(odometry->pose.pose.position.x, odometry->pose.pose.position.y, odometry->pose.pose.position.z));
    tfBroadcasterPointer->sendTransform(odomTrans_vechle);

}

void callbackMap( const nav_msgs::OccupancyGrid::ConstPtr &map)
{
    global_map_origin = *map;    
}

void imageCallback(const sensor_msgs::ImageConstPtr& msg)
{
    cv::Mat image;
    try{
        image=cv_bridge::toCvShare(msg, "mono8")->image.clone();
        
    }
    catch (cv_bridge::Exception& e){
        ROS_ERROR("Could not convert from '%s' to 'mono8'.", msg->encoding.c_str());
        return  ;
    }
    g_prejector.reset_mask(image,2);

}

void depth_camera_cloud_callback(const sensor_msgs::PointCloud2::ConstPtr& cloud_msg){

    pcl::PCLPointCloud2::Ptr cloud_camera(new pcl::PCLPointCloud2);
    pcl_conversions::toPCL(*cloud_msg, *cloud_camera);

    // 创建目标PointCloudXYZI
    pcl::PointCloud<pcl::PointXYZ>::Ptr cloud_rgb(new pcl::PointCloud<pcl::PointXYZ>());
    pcl::fromPCLPointCloud2 (*cloud_camera, *cloud_rgb);

    pcl::PointCloud<pcl::PointXYZI>::Ptr pcl_cloud(new pcl::PointCloud<pcl::PointXYZI>());
    pcl_cloud->points.resize(cloud_rgb->size());
    for (size_t i = 0; i < cloud_rgb->points.size(); i++) {
        pcl_cloud->points[i].x = cloud_rgb->points[i].x;
        pcl_cloud->points[i].y = cloud_rgb->points[i].y;
        pcl_cloud->points[i].z = cloud_rgb->points[i].z;
    }

    Eigen::Quaterniond quaternion(g_lidar2camera[6],g_lidar2camera[3],g_lidar2camera[4],g_lidar2camera[5]);    
    Eigen::Matrix4d transform = Eigen::Matrix4d::Identity();
    transform.block<3,3>(0,0) = quaternion.toRotationMatrix();
    transform.block<3,1>(0,3) = g_lidar2camera.topRows(3);

    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud_lidar(new pcl::PointCloud<pcl::PointXYZI>());
    pcl::transformPointCloud(*pcl_cloud, *cloud_lidar, transform);  //转到lidar下坐标系下

    pcl::PointCloud<pcl::PointXYZI>::Ptr cloud_lidar_filter(new pcl::PointCloud<pcl::PointXYZI>());
    pcl::VoxelGrid<pcl::PointXYZI> sor;
    sor.setInputCloud(cloud_lidar);
    sor.setLeafSize(0.05f, 0.05f, 0.05f);
    sor.filter(*cloud_lidar_filter);

    g_depth_cloud.reset(cloud_lidar_filter,0.1);

}

int main(int argc, char**argv) {

    ros::init(argc, argv, "terrain_analysis");
    ros::NodeHandle nh;
    ros::NodeHandle private_nh("~");

    private_nh.param("cloud_topic", cloud_topic, std::string("/lslidar_point_cloud"));
    private_nh.param("odom_topic", odom_topic, std::string("/Odometry"));
    private_nh.param("terrain_cloud_topic", terrain_cloud_topic, std::string("/terrain_map"));
    private_nh.param("resolution", resolution, 0.1);
    private_nh.param("log_odds_increase", log_odds_increase, 0.4);
    private_nh.param("log_odds_decrease", log_odds_decrease, 0.2);
    private_nh.param("beam_num", beam_num, 2048);
    private_nh.param("width", width, 40.0);
    private_nh.param("updatetime", updatetime, 1.0);
    private_nh.param("occupancy_threshold", occupancy_threshold, 0.7);
    private_nh.param("vehicle_length", vehicle_length, 1.0);
    private_nh.param("vehicle_width", vehicle_width, 0.5);
    private_nh.param("z_max", z_max, 1.0);
    private_nh.param("z_min", z_min, -1.0);
    private_nh.param("using_map", using_map, false);


    //相机内参
    double fx=0.0,fy=0.0,cx=0.0,cy=0.0;
    private_nh.param("fx", fx, 0.0);
    private_nh.param("fy", fy, 0.0);
    private_nh.param("cx", cx, 0.0);
    private_nh.param("cy", cy, 0.0);

    //相机、雷达外参
    std::vector<double> default_q = {0.0, 0.0, 0.0,1.0};
    std::vector<double> default_t = {0.0, 0.0, 0.0};
    std::vector<double> q_lidar2camera,t_lidar2camera;
    private_nh.param("q_lidar2camera", q_lidar2camera, default_q);
    private_nh.param("t_lidar2camera", t_lidar2camera, default_t);
    g_lidar2camera.resize(7);
    g_lidar2camera << t_lidar2camera[0], t_lidar2camera[1], t_lidar2camera[2],
            q_lidar2camera[0], q_lidar2camera[1], q_lidar2camera[2],q_lidar2camera[3];   // tx,ty,tz , qx,qy,qz,qw
    g_prejector.init(Eigen::Vector4d(fx,fy,cx,cy),Eigen::VectorXd(),g_lidar2camera);


    std::string mask_topic;
    private_nh.param("mask_image_topic", mask_topic,std::string("/mask"));
    image_transport::ImageTransport it(private_nh);
    debug_image_pub = it.advertise("/preject_img", 1);
    image_transport::Subscriber image_sub = it.subscribe(mask_topic, 1, imageCallback);

    std::cout<<"--------------------------------------------------------------------"<<std::endl;
    std::cout<<"  tq:"<<g_lidar2camera.transpose()<<std::endl;
    std::cout<<"--------------------------------------------------------------------"<<std::endl;

    sub_cloud = nh.subscribe<sensor_msgs::PointCloud2>(cloud_topic, 1, callbackCloud);
    sub_odom = nh.subscribe<nav_msgs::Odometry>(odom_topic, 1, callbackOdom);
    sub_map = nh.subscribe<nav_msgs::OccupancyGrid>("/map", 1, callbackMap);
    pub_map = nh.advertise<nav_msgs::OccupancyGrid>("/map_edit", 1);

    std::string depth_cloud_topic;
    private_nh.param("depth_cloud_topic", depth_cloud_topic, std::string("/camera_depth_points"));
    ros::Subscriber sub_depth_cloud = nh.subscribe<sensor_msgs::PointCloud2>(depth_cloud_topic, 1, depth_camera_cloud_callback);

    pub_terrain_cloud = nh.advertise<sensor_msgs::PointCloud2>(terrain_cloud_topic, 1);
    pub_odometry = nh.advertise<nav_msgs::Odometry> ("/state_estimation", 1);
    tf::TransformBroadcaster tfBroadcaster;
    tfBroadcasterPointer = &tfBroadcaster;
    Dynamic.initialization(private_nh, width, resolution, occupancy_threshold, beam_num, log_odds_increase, 
                            log_odds_decrease, updatetime, vehicle_length, vehicle_width, z_max, z_min);
    cout << "Initialize successfully!" << endl;
    
    ros::Rate rate(10);
    bool status = ros::ok();
    while (status) 
    { 
        ros::spinOnce();
        status = ros::ok();
		rate.sleep();
    }
    return 0;
}
