// Generated by gencpp from file local_planner/NavigationTarget.msg
// DO NOT EDIT!


#ifndef LOCAL_PLANNER_MESSAGE_NAVIGATIONTARGET_H
#define LOCAL_PLANNER_MESSAGE_NAVIGATIONTARGET_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace local_planner
{
template <class ContainerAllocator>
struct NavigationTarget_
{
  typedef NavigationTarget_<ContainerAllocator> Type;

  NavigationTarget_()
    : nav_mode(0)
    , point_id(0)
    , pose_x(0.0)
    , pose_y(0.0)
    , pose_z(0.0)
    , yaw(0.0)
    , point_info(0)
    , gait(0)
    , speed(0)
    , manner(0)
    , obsmode(0)
    , navmode(0)  {
    }
  NavigationTarget_(const ContainerAllocator& _alloc)
    : nav_mode(0)
    , point_id(0)
    , pose_x(0.0)
    , pose_y(0.0)
    , pose_z(0.0)
    , yaw(0.0)
    , point_info(0)
    , gait(0)
    , speed(0)
    , manner(0)
    , obsmode(0)
    , navmode(0)  {
  (void)_alloc;
    }



   typedef int32_t _nav_mode_type;
  _nav_mode_type nav_mode;

   typedef int32_t _point_id_type;
  _point_id_type point_id;

   typedef double _pose_x_type;
  _pose_x_type pose_x;

   typedef double _pose_y_type;
  _pose_y_type pose_y;

   typedef double _pose_z_type;
  _pose_z_type pose_z;

   typedef double _yaw_type;
  _yaw_type yaw;

   typedef int32_t _point_info_type;
  _point_info_type point_info;

   typedef int32_t _gait_type;
  _gait_type gait;

   typedef int32_t _speed_type;
  _speed_type speed;

   typedef int32_t _manner_type;
  _manner_type manner;

   typedef int32_t _obsmode_type;
  _obsmode_type obsmode;

   typedef int32_t _navmode_type;
  _navmode_type navmode;





  typedef boost::shared_ptr< ::local_planner::NavigationTarget_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::local_planner::NavigationTarget_<ContainerAllocator> const> ConstPtr;

}; // struct NavigationTarget_

typedef ::local_planner::NavigationTarget_<std::allocator<void> > NavigationTarget;

typedef boost::shared_ptr< ::local_planner::NavigationTarget > NavigationTargetPtr;
typedef boost::shared_ptr< ::local_planner::NavigationTarget const> NavigationTargetConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::local_planner::NavigationTarget_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::local_planner::NavigationTarget_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::local_planner::NavigationTarget_<ContainerAllocator1> & lhs, const ::local_planner::NavigationTarget_<ContainerAllocator2> & rhs)
{
  return lhs.nav_mode == rhs.nav_mode &&
    lhs.point_id == rhs.point_id &&
    lhs.pose_x == rhs.pose_x &&
    lhs.pose_y == rhs.pose_y &&
    lhs.pose_z == rhs.pose_z &&
    lhs.yaw == rhs.yaw &&
    lhs.point_info == rhs.point_info &&
    lhs.gait == rhs.gait &&
    lhs.speed == rhs.speed &&
    lhs.manner == rhs.manner &&
    lhs.obsmode == rhs.obsmode &&
    lhs.navmode == rhs.navmode;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::local_planner::NavigationTarget_<ContainerAllocator1> & lhs, const ::local_planner::NavigationTarget_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace local_planner

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::local_planner::NavigationTarget_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::local_planner::NavigationTarget_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::local_planner::NavigationTarget_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::local_planner::NavigationTarget_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::local_planner::NavigationTarget_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::local_planner::NavigationTarget_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::local_planner::NavigationTarget_<ContainerAllocator> >
{
  static const char* value()
  {
    return "1a581c62d1a7cd390f05dc83155acdfb";
  }

  static const char* value(const ::local_planner::NavigationTarget_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x1a581c62d1a7cd39ULL;
  static const uint64_t static_value2 = 0x0f05dc83155acdfbULL;
};

template<class ContainerAllocator>
struct DataType< ::local_planner::NavigationTarget_<ContainerAllocator> >
{
  static const char* value()
  {
    return "local_planner/NavigationTarget";
  }

  static const char* value(const ::local_planner::NavigationTarget_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::local_planner::NavigationTarget_<ContainerAllocator> >
{
  static const char* value()
  {
    return "int32 nav_mode # 1:导航启动 0：导航停止\n"
"\n"
"int32 point_id   # 点位编号\n"
"float64 pose_x\n"
"float64 pose_y\n"
"float64 pose_z # 预留\n"
"float64 yaw\n"
"int32 point_info   # 任务点属性 0: 过渡点 1: 任务点 2: 充电准备点 3: 充电点\n"
"int32 gait   # 到达目标点使用的步态 0: 普通步态 1: 楼梯步态 2: 防滑步态 3: 匍匐步态\n"
"int32 speed   # 速度设置 0: 普通速 1: 低速 2: 高速\n"
"int32 manner   # 运动方式 0: 正走 1: 逆走\n"
"int32 obsmode   # 障碍处理模式 0: 避障 1: 停障\n"
"int32 navmode    # 导航模式 0: 直线导航 1: 自主导航\n"
"\n"
;
  }

  static const char* value(const ::local_planner::NavigationTarget_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::local_planner::NavigationTarget_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.nav_mode);
      stream.next(m.point_id);
      stream.next(m.pose_x);
      stream.next(m.pose_y);
      stream.next(m.pose_z);
      stream.next(m.yaw);
      stream.next(m.point_info);
      stream.next(m.gait);
      stream.next(m.speed);
      stream.next(m.manner);
      stream.next(m.obsmode);
      stream.next(m.navmode);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct NavigationTarget_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::local_planner::NavigationTarget_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::local_planner::NavigationTarget_<ContainerAllocator>& v)
  {
    s << indent << "nav_mode: ";
    Printer<int32_t>::stream(s, indent + "  ", v.nav_mode);
    s << indent << "point_id: ";
    Printer<int32_t>::stream(s, indent + "  ", v.point_id);
    s << indent << "pose_x: ";
    Printer<double>::stream(s, indent + "  ", v.pose_x);
    s << indent << "pose_y: ";
    Printer<double>::stream(s, indent + "  ", v.pose_y);
    s << indent << "pose_z: ";
    Printer<double>::stream(s, indent + "  ", v.pose_z);
    s << indent << "yaw: ";
    Printer<double>::stream(s, indent + "  ", v.yaw);
    s << indent << "point_info: ";
    Printer<int32_t>::stream(s, indent + "  ", v.point_info);
    s << indent << "gait: ";
    Printer<int32_t>::stream(s, indent + "  ", v.gait);
    s << indent << "speed: ";
    Printer<int32_t>::stream(s, indent + "  ", v.speed);
    s << indent << "manner: ";
    Printer<int32_t>::stream(s, indent + "  ", v.manner);
    s << indent << "obsmode: ";
    Printer<int32_t>::stream(s, indent + "  ", v.obsmode);
    s << indent << "navmode: ";
    Printer<int32_t>::stream(s, indent + "  ", v.navmode);
  }
};

} // namespace message_operations
} // namespace ros

#endif // LOCAL_PLANNER_MESSAGE_NAVIGATIONTARGET_H
