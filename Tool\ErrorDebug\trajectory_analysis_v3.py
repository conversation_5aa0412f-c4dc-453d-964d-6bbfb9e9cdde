#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rosbag
import numpy as np
import matplotlib.pyplot as plt
from geometry_msgs.msg import PoseStamped
from nav_msgs.msg import Odometry
import tf.transformations as tf_trans
import argparse
from datetime import datetime

class TrajectoryAnalyzer:
    def __init__(self, bag_file):
        self.bag_file = bag_file
        self.desired_x = []
        self.desired_y = []
        self.desired_yaw = []
        self.actual_x = []
        self.actual_y = []
        self.actual_yaw = []
        self.timestamps = []
        
    def quaternion_to_yaw(self, quaternion):
        """Convert quaternion to yaw angle"""
        euler = tf_trans.euler_from_quaternion([quaternion.x, quaternion.y, quaternion.z, quaternion.w])
        return euler[2]
    
    def process_bag(self):
        """Process the rosbag file and extract trajectory data"""
        print(f"Processing bag file: {self.bag_file}")
        bag = rosbag.Bag(self.bag_file)
        
        # Initialize variables for time synchronization
        last_goal_time = None
        last_odom_time = None
        goal_msg = None
        
        for topic, msg, t in bag.read_messages(topics=['/local_goal', '/Odometry']):
            if topic == '/local_goal':
                goal_msg = msg
                last_goal_time = t
            elif topic == '/Odometry' and goal_msg is not None:
                # Store actual trajectory data
                self.actual_x.append(msg.pose.pose.position.x)
                self.actual_y.append(msg.pose.pose.position.y)
                actual_yaw = self.quaternion_to_yaw(msg.pose.pose.orientation)
                self.actual_yaw.append(actual_yaw)
                
                # Store desired trajectory data
                self.desired_x.append(goal_msg.pose.position.x)
                self.desired_y.append(goal_msg.pose.position.y)
                desired_yaw = self.quaternion_to_yaw(goal_msg.pose.orientation)
                self.desired_yaw.append(desired_yaw)
                
                # Store timestamp
                self.timestamps.append(t.to_sec())
        
        bag.close()
        
    def calculate_errors(self):
        """Calculate position and orientation errors"""
        self.position_errors = []
        self.yaw_errors = []
        
        for i in range(len(self.actual_x)):
            # Calculate position error
            dx = self.desired_x[i] - self.actual_x[i]
            dy = self.desired_y[i] - self.actual_y[i]
            position_error = np.sqrt(dx*dx + dy*dy)
            self.position_errors.append(position_error)
            
            # Calculate yaw error
            yaw_error = self.desired_yaw[i] - self.actual_yaw[i]
            # Normalize yaw error to [-pi, pi]
            yaw_error = np.arctan2(np.sin(yaw_error), np.cos(yaw_error))
            self.yaw_errors.append(abs(yaw_error))
    
    def plot_results(self):
        """Plot trajectory comparison and errors"""
        # Create figure with subplots
        fig = plt.figure(figsize=(20, 15))
        
        # Plot 1: Trajectory comparison (XY plane)
        ax1 = fig.add_subplot(331)
        ax1.plot(self.desired_x, self.desired_y, 'r--', label='Desired Path')
        ax1.plot(self.actual_x, self.actual_y, 'b-', label='Actual Path')
        ax1.set_title('Trajectory Comparison (XY Plane)')
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.legend()
        ax1.grid(True)
        
        # Plot 2: X position comparison over time
        ax2 = fig.add_subplot(332)
        ax2.plot(self.timestamps, self.desired_x, 'r--', label='Desired X')
        ax2.plot(self.timestamps, self.actual_x, 'b-', label='Actual X')
        ax2.set_title('X Position Comparison')
        ax2.set_xlabel('Time (s)')
        ax2.set_ylabel('Position (m)')
        ax2.legend()
        ax2.grid(True)
        
        # Plot 3: Y position comparison over time
        ax3 = fig.add_subplot(333)
        ax3.plot(self.timestamps, self.desired_y, 'r--', label='Desired Y')
        ax3.plot(self.timestamps, self.actual_y, 'b-', label='Actual Y')
        ax3.set_title('Y Position Comparison')
        ax3.set_xlabel('Time (s)')
        ax3.set_ylabel('Position (m)')
        ax3.legend()
        ax3.grid(True)
        
        # Plot 4: X position error over time
        ax4 = fig.add_subplot(334)
        x_errors = [dx for dx in np.array(self.desired_x) - np.array(self.actual_x)]
        ax4.plot(self.timestamps, x_errors, 'g-')
        ax4.set_title('X Position Error Over Time')
        ax4.set_xlabel('Time (s)')
        ax4.set_ylabel('Error (m)')
        ax4.grid(True)
        
        # Plot 5: Y position error over time
        ax5 = fig.add_subplot(335)
        y_errors = [dy for dy in np.array(self.desired_y) - np.array(self.actual_y)]
        ax5.plot(self.timestamps, y_errors, 'g-')
        ax5.set_title('Y Position Error Over Time')
        ax5.set_xlabel('Time (s)')
        ax5.set_ylabel('Error (m)')
        ax5.grid(True)
        
        # Plot 6: Total position error over time
        ax6 = fig.add_subplot(336)
        ax6.plot(self.timestamps, self.position_errors, 'g-')
        ax6.set_title('Total Position Error Over Time')
        ax6.set_xlabel('Time (s)')
        ax6.set_ylabel('Error (m)')
        ax6.grid(True)
        
        # Plot 7: Yaw error over time
        ax7 = fig.add_subplot(337)
        ax7.plot(self.timestamps, self.yaw_errors, 'm-')
        ax7.set_title('Yaw Error Over Time')
        ax7.set_xlabel('Time (s)')
        ax7.set_ylabel('Error (rad)')
        ax7.grid(True)
        
        # Plot 8: X and Y error distribution
        ax8 = fig.add_subplot(338)
        ax8.hist(x_errors, bins=50, alpha=0.5, label='X Error')
        ax8.hist(y_errors, bins=50, alpha=0.5, label='Y Error')
        ax8.set_title('X and Y Error Distribution')
        ax8.set_xlabel('Error (m)')
        ax8.set_ylabel('Frequency')
        ax8.legend()
        ax8.grid(True)
        
        # Plot 9: Yaw error distribution
        ax9 = fig.add_subplot(339)
        ax9.hist(self.yaw_errors, bins=50, alpha=0.5, label='Yaw Error')
        ax9.set_title('Yaw Error Distribution')
        ax9.set_xlabel('Error (rad)')
        ax9.set_ylabel('Frequency')
        ax9.legend()
        ax9.grid(True)
        
        plt.tight_layout()
        
        # Save plot with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plt.savefig(f'trajectory_analysis_{timestamp}.png')
        plt.show()
        
    def print_statistics(self):
        """Print statistical analysis of errors"""
        print("\nTrajectory Analysis Statistics:")
        print("-" * 30)
        
        # Position error statistics
        pos_mean = np.mean(self.position_errors)
        pos_std = np.std(self.position_errors)
        pos_max = np.max(self.position_errors)
        
        print(f"Position Error:")
        print(f"  Mean: {pos_mean:.3f} m")
        print(f"  Std Dev: {pos_std:.3f} m")
        print(f"  Max: {pos_max:.3f} m")
        
        # Yaw error statistics
        yaw_mean = np.mean(self.yaw_errors)
        yaw_std = np.std(self.yaw_errors)
        yaw_max = np.max(self.yaw_errors)
        
        print(f"\nYaw Error:")
        print(f"  Mean: {yaw_mean:.3f} rad")
        print(f"  Std Dev: {yaw_std:.3f} rad")
        print(f"  Max: {yaw_max:.3f} rad")
        
        # PID tuning suggestions
        print("\nPID Tuning Suggestions:")
        print("-" * 30)
        if pos_mean > 0.1:
            print("Position control may need adjustment:")
            if pos_std > 0.05:
                print("  - Consider increasing D gain to reduce oscillations")
            if pos_max > 0.2:
                print("  - Consider increasing P gain to reduce steady-state error")
        
        if yaw_mean > 0.1:
            print("Yaw control may need adjustment:")
            if yaw_std > 0.05:
                print("  - Consider increasing D gain to reduce oscillations")
            if yaw_max > 0.2:
                print("  - Consider increasing P gain to reduce steady-state error")

def main():
    parser = argparse.ArgumentParser(description='Analyze trajectory deviation from rosbag data')
    parser.add_argument('bag_file', help='Path to the rosbag file')
    args = parser.parse_args()
    
    analyzer = TrajectoryAnalyzer(args.bag_file)
    analyzer.process_bag()
    analyzer.calculate_errors()
    analyzer.plot_results()
    analyzer.print_statistics()

if __name__ == '__main__':
    main() 