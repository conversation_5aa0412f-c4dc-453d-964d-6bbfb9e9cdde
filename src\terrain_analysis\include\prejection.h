#ifndef __REPREJECTION__H__
#define __REPREJECTION__H__


#include <Eigen/Eigen>
#include <pcl/point_cloud.h>
#include <opencv2/opencv.hpp>
#include <vector>
#include <chrono>

#include "threadsafe_timeout.hpp"

/*
* 将雷达坐标系下的三维空间点投影到图像坐标
*/


class Reprejection{
public:
    Reprejection();
    
    ~Reprejection();
    Reprejection(const Reprejection& other)=default;
    void init(Eigen::Vector4d K,Eigen::VectorXd dist,Eigen::VectorXd t_lidar_camera);
    bool preject(Eigen::Vector3d point3d,Eigen::Vector2d& image_pt);

    void reset_mask(cv::Mat mask,float timeout=0.1);
    int  pixel_type(Eigen::Vector2d pixel);
    bool timeout();

    cv::Mat debug_img();
    void clear_debug();
public:    
    Eigen::Matrix3d K_;         //内参矩阵
    Eigen::VectorXd dist_;      //畸变系数
    Eigen::Matrix3d r_l2c_;     //相机在雷达坐标系下的旋转
    Eigen::Vector3d t_l2c_;     //相机在雷达坐标系下的平移
    ThreadSafeTimed<cv::Mat> mask_;

    ThreadSafeTimed<cv::Mat> debug_;
};

#endif