#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <ros/ros.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float32.h>
#include <nav_msgs/Path.h>
#include <std_msgs/Float32MultiArray.h>
#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Int64.h>
#include <nav_msgs/Odometry.h>
#include <geometry_msgs/PointStamped.h>
#include <geometry_msgs/PolygonStamped.h>
#include <sensor_msgs/Imu.h>
#include <sensor_msgs/PointCloud2.h>
#include <sensor_msgs/Joy.h>
#include <tf/transform_datatypes.h>
#include <tf/transform_broadcaster.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/time.h>
#include <pcl/registration/icp.h>
#include <pcl/io/pcd_io.h>
#include <local_planner/NavigationResult.h>
#include <local_planner/NavigationTarget.h>

using namespace std;

const double PI = 3.1415926;

double goalX = 0;
double goalY = 0;
double goalZ = 0;
double goalYaw = 0;
double sensorOffsetX = 0;
double sensorOffsetY = 0;
int nav_start = 0;
int info = 0;
int obs_mode = 0;
int id = 0;
float vehicleX = 0;
float vehicleY = 0;
float vehicleZ = 0;
float vehicleRoll = 0;
float vehiclePitch = 0;
float vehicleYaw = 0;
double odomTime = 0;
bool newTerrainCloud = false;
double vehicleLength = 1.0;
double vehicleWidth = 0.5;
double obstacleHeightThre = 0.15;
int obsnumThre = 2;
double start_time, end_time;
bool init;
double targetX, targetY, targetZ, targetYaw;
double maxSpeed;
bool twoWayDrive = false;
double adjacentRange = 3;
double replan_time = 5;
pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloud(new pcl::PointCloud<pcl::PointXYZI>());
pcl::PointCloud<pcl::PointXYZI>::Ptr terrainCloudCrop(new pcl::PointCloud<pcl::PointXYZI>());

std_msgs::Bool adjustmode;
std_msgs::Int8 safetystop;

void odometryHandler(const nav_msgs::Odometry::ConstPtr& odom)//载体当前位姿信息回调函数
{
  odomTime = odom->header.stamp.toSec();
  //计算横滚、俯仰和偏航角
  double roll, pitch, yaw;
  geometry_msgs::Quaternion geoQuat = odom->pose.pose.orientation;
  tf::Matrix3x3(tf::Quaternion(geoQuat.x, geoQuat.y, geoQuat.z, geoQuat.w)).getRPY(roll, pitch, yaw);
  vehicleRoll = roll;
  vehiclePitch = pitch;
  vehicleYaw = yaw;
  vehicleX = odom->pose.pose.position.x - cos(vehicleYaw) * sensorOffsetX + sin(vehicleYaw) * sensorOffsetY;
  vehicleY = odom->pose.pose.position.y - sin(vehicleYaw) * sensorOffsetX - cos(vehicleYaw) * sensorOffsetY;
  vehicleZ = odom->pose.pose.position.z;
}

void terrainCloudHandler(const sensor_msgs::PointCloud2ConstPtr& terrainCloud2)//可通行区域点云回调函数
{
    terrainCloud->clear();
    pcl::fromROSMsg(*terrainCloud2, *terrainCloud);
    pcl::PointXYZI point;
    terrainCloudCrop->clear();
    int terrainCloudSize = terrainCloud->points.size();
    for (int i = 0; i < terrainCloudSize; i++) 
    {
      point = terrainCloud->points[i];
      if (point.intensity > obstacleHeightThre)//筛选出障碍物点云
      {
        terrainCloudCrop->push_back(point);
      }
    }
    //点云信息更新
    newTerrainCloud = true;  
}

void targetHandler(const geometry_msgs::PoseStamped::ConstPtr& target)//导航目标点及导航参数回调函数
{
  nav_start = 1;
  //id = target->point_id;
  targetX = target->pose.position.x;
  targetY = target->pose.position.y;
  //targetZ = target->pose.position.z;
  tf::Quaternion quat;
  tf::quaternionMsgToTF(target->pose.orientation, quat);
  double roll, pitch, yaw;//定义存储r\p\y的容器
  tf::Matrix3x3(quat).getRPY(roll, pitch, yaw);//进行转换
  targetYaw = yaw;
  //info = target->point_info;
  obs_mode = target->obsmode;
   //普通速、低速、高速切换
   /*
  if (target->speed == 0)
  maxSpeed = 0.8;
  else if (target->speed == 1)
  maxSpeed = 0.5;
  else if (target->speed == 2)
  maxSpeed = 1.0;
  */
  maxSpeed = 0.8;
  init = true;
  adjustmode.data = false;
  string mode_obs, info_point;
  if (info == 0)
    info_point = "过渡点";
  else if (info == 1)
    info_point = "任务点";
  if (obs_mode == 0)
    mode_obs = "避障模式";
  else if (obs_mode == 1)
    mode_obs = "停障模式";
  std::cout<<id<<"号目标点,x坐标:"<<targetX<<",Y坐标:"<<targetY<<",Z坐标:"<<targetZ<<",航向:"<<targetYaw<<",任务点信息:"<<info_point<<",障碍物模式:"<<mode_obs<<std::endl;
}

void goalHandler(const geometry_msgs::PoseStamped::ConstPtr& goal)//局部目标点回调函数
{
  goalX = goal->pose.position.x;
  goalY = goal->pose.position.y;
  goalZ = goal->pose.position.z;
  adjustmode.data = false;
  if (init)
  {
    std::cout<<"***********进入局部规划模式***********"<<std::endl;
    init = false;
  }
}

int main(int argc, char** argv)
{
  ros::init(argc, argv, "obstaclestop");
  ros::NodeHandle nh;
  ros::NodeHandle private_nh("~");
  private_nh.param("obstacleHeightThre", obstacleHeightThre, 0.1);
  private_nh.param("vehicleLength", vehicleLength, 1.2);
  private_nh.param("vehicleWidth", vehicleWidth, 0.8);
  private_nh.param("obsnumThre", obsnumThre, 2);
  private_nh.param("adjacentRange", adjacentRange, 3.0);
  private_nh.param("replan_time", replan_time, 120.0);
  private_nh.param("sensorOffsetX", sensorOffsetX, 0.0);
  private_nh.param("sensorOffsetY", sensorOffsetY, 0.0);
  //订阅当前状态的载体位姿信息
  ros::Subscriber subOdometry = nh.subscribe<nav_msgs::Odometry>("/state_estimation", 1, odometryHandler);
  //订阅可通行区域点云信息
  ros::Subscriber subTerrainCloud = nh.subscribe<sensor_msgs::PointCloud2>("/terrain_map", 5, terrainCloudHandler);
  //订阅局部目标点信息
  ros::Subscriber subGoal = nh.subscribe<geometry_msgs::PoseStamped> ("/local_goal", 1, goalHandler);
  //订阅导航目标点及导航参数信息
  ros::Subscriber subTarget = nh.subscribe<geometry_msgs::PoseStamped> ("/navigation_target", 1, targetHandler);
  //发布粗调精调模式信息
  ros::Publisher pubMode = nh.advertise<std_msgs::Bool> ("/adjustmode", 5);
  //发布停止信息
  ros::Publisher pubStop = nh.advertise<std_msgs::Int8> ("/stop", 1 );
  //发布局部轨迹信息
  ros::Publisher pubPath = nh.advertise<nav_msgs::Path> ("/path", 1);
  //发布重规划信息
  ros::Publisher pubReplan = nh.advertise<std_msgs::Int8> ("/replan", 1 );
  //发布导航状态信息
  ros::Publisher pubResult = nh.advertise<local_planner::NavigationResult> ("/navigation_result", 1);
  //初始化切入粗调模式
  adjustmode.data = true;
  ros::Rate rate(100);
  bool status = ros::ok();
  while (status) 
  {
    ros::spinOnce();
    if (!adjustmode.data && newTerrainCloud && nav_start == 1 && obs_mode == 1 && !init)//进入停障模式
    {
      newTerrainCloud = false;
      //根据当前载体位置与终点位置判断进入的导航模式
      double distance = sqrt((targetX - vehicleX) * (targetX - vehicleX) + (targetY - vehicleY) * (targetY - vehicleY));
      if (distance < 0.3)//进入精调模式的距离
      {
        nav_start = 0;
        if (info == 1)//任务点需切精调
      	{
          adjustmode.data = true;
          std::cout<<"***********进入位姿矫正模式***********"<<std::endl;
          safetystop.data = 0;
        }
        else if (info == 0)//过渡点不切精调
        {
          adjustmode.data = false;
          std::cout<<"************已到达目标点************"<<std::endl;
          safetystop.data = 0;
          local_planner::NavigationResult result;
	        result.point_id = id;
	        result.target_pose_x = targetX;
	        result.target_pose_y = targetY;
	        result.target_pose_z = targetZ;
	        result.target_yaw = targetYaw;
	        result.current_pose_x = vehicleX;
	        result.current_pose_y = vehicleY;
	        result.current_pose_z = vehicleZ;
	        result.current_yaw = vehicleYaw;
	        result.nav_state = 0;
	        pubResult.publish(result);
        }
      }
      else//导航模式
      {
        adjustmode.data = false;
        safetystop.data = 5;
      }
      pubMode.publish(adjustmode);
      float relativeGoalX = ((goalX - vehicleX) * cos(vehicleYaw) + (goalY - vehicleY) * sin(vehicleYaw));
      float relativeGoalY = (-(goalX - vehicleX) * sin(vehicleYaw) + (goalY - vehicleY) * cos(vehicleYaw));
      float relativeGoalDis = sqrt(relativeGoalX * relativeGoalX + relativeGoalY * relativeGoalY);
      float joyDir = atan2(relativeGoalY, relativeGoalX) * 180 / PI;
      nav_msgs::Path path;
      if(relativeGoalDis > adjacentRange * maxSpeed) relativeGoalDis = adjacentRange * maxSpeed;
      path.poses.resize(int(relativeGoalDis / 0.01));
      for (int i = 0; i < int(relativeGoalDis / 0.01); i++)//将目标点前方一段轨迹作为当前时刻的局部轨迹
      {
        path.poses[i].pose.position.x =  i * 0.01 * cos(joyDir / 180 * PI);
        path.poses[i].pose.position.y =  i * 0.01 * sin(joyDir / 180 * PI);
        path.poses[i].pose.position.z = 0;
      }
      int obsnum = 0;
      if (abs(joyDir) > 10)//载体处于自转状态
      {
        for (int i = 0; i < int(terrainCloudCrop->points.size()); i++)
        {
          double obs_dis = sqrt(pow(terrainCloudCrop->points[i].x - vehicleX,2) + pow(terrainCloudCrop->points[i].y - vehicleY,2));
          if (obs_dis <= 0.5 * sqrt(pow(vehicleWidth,2)+pow(vehicleLength,2)))//判断载体四周是否有障碍物
          {
            obsnum +=1;
          }
        }
      }
      else//载体处于前进后退状态
      {
        for (int i = 0; i < int(terrainCloudCrop->points.size()); i++)
        {
          for (int j = 0; j < int(relativeGoalDis / 0.01); j++)
          {
            double pointx = terrainCloudCrop->points[i].x;
            double pointy = terrainCloudCrop->points[i].y;
            double obs_dis = sqrt(pow(pointx - path.poses[j].pose.position.x, 2) + pow(pointy - path.poses[j].pose.position.y, 2));
            if (obs_dis <= 0.5 * vehicleWidth)//判断载体的每个局部轨迹点四周是否有障碍物
            {
              obsnum +=1;
              break;
            }
          }
        }
      }
      //若障碍物点云数量超过阈值则发布停止信息
      if (obsnum > obsnumThre)
      {
        path.poses.resize(1);
        path.poses[0].pose.position.x = 0;
        path.poses[0].pose.position.y = 0;
        path.poses[0].pose.position.z = 0;
        end_time = pcl::getTime();
        if (end_time - start_time > replan_time)
        {
          std_msgs::Int8 replan;
          replan.data = 1;
          pubReplan.publish(replan);
          nav_start = 0;
        }
        safetystop.data = 0;
      } 
      else
      {
        start_time = pcl::getTime();
        end_time = pcl::getTime();
        std_msgs::Int8 replan;
        replan.data = 0;
        pubReplan.publish(replan);
      }
      //发布局部轨迹信息
      path.header.stamp = ros::Time().fromSec(odomTime);
      path.header.frame_id = "vehicle";
      pubPath.publish(path);
      pubStop.publish(safetystop);
    }
    rate.sleep();
  }
  return 0;
}
