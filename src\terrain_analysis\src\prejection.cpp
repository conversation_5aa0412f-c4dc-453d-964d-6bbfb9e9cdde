

#include "prejection.h"
#include<opencv2/opencv.hpp>

Reprejection::Reprejection(){}

void Reprejection::init(Eigen::Vector4d K,Eigen::VectorXd dist,Eigen::VectorXd t_lidar_camera){

    Eigen::Quaterniond quaternion(t_lidar_camera[6],t_lidar_camera[3],t_lidar_camera[4],t_lidar_camera[5]);
    r_l2c_=quaternion.toRotationMatrix().transpose();
    t_l2c_= -(r_l2c_*t_lidar_camera.topRows(3));     // t_lidar_camera    x y z w 

    K_ << K[0],0,K[2],
          0,K[1],K[3],
          0,   0,   1;

    dist_=dist;

}
Reprejection::~Reprejection(){

}

bool Reprejection::preject(Eigen::Vector3d point3d,Eigen::Vector2d& image_pt){

    Eigen::Vector3d point_camera = r_l2c_ * point3d +t_l2c_;
    
    if (point_camera[2] <=0.0){
        return false;
    }
 
    Eigen::Vector3d pixel_pt = K_ * point_camera;
    image_pt[0] = pixel_pt[0] / pixel_pt[2] ;
    image_pt[1] = pixel_pt[1] / pixel_pt[2] ;
    
    if(image_pt[0]<0 || image_pt[1]<0){
        return false;
    }

    if(!debug_.timeout()  && !mask_.timeout() ){
 
        if( pixel_type(image_pt) !=0){
            cv::circle(debug_(), cv::Point(image_pt[0],image_pt[1]), 1, cv::Scalar(0, 0, 255), -1);
        }else{
            cv::circle(debug_(), cv::Point(image_pt[0],image_pt[1]), 1, cv::Scalar(0, 255, 0), -1);
        }
        
    }

    return true;

}

void Reprejection::reset_mask(cv::Mat mask,float timeout){
    if(mask.cols ==0 || mask.rows == 0){
        return;
    }
    mask_.reset(mask.clone(),timeout);

    cv::Mat debug_img;
    cv::cvtColor(mask.clone(), debug_img, cv::COLOR_GRAY2BGR);
    debug_.reset(debug_img,timeout);
}

cv::Mat Reprejection::debug_img(){
    if(!debug_.timeout()){
        return debug_.get();
    }else{
        return cv::Mat(cv::Size(480,640), CV_8UC3, cv::Scalar(0, 0, 0));
    }
}

void Reprejection::clear_debug(){
    if(mask_.timeout()==false){
        cv::Mat debug_img;
        cv::cvtColor(mask_.get(), debug_img, cv::COLOR_GRAY2BGR);
        debug_.reset(debug_img,2);
    }
}

bool Reprejection::timeout(){
    
    return mask_.timeout();

}


int Reprejection::pixel_type(Eigen::Vector2d pixel){
    int row=int(pixel[1]);
    int col=int(pixel[0]);
    cv::Mat mask=mask_.get();
    if(mask.cols >col && mask.rows>row && row>=0 && col>=0){
        return mask.at<uchar>(row,col);
    }
    return 0;
}