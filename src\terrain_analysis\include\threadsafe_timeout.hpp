//
// Created by zx on 22-12-1.
//

#ifndef _CPP_DEFINE_TIME_DATA_H_
#define _CPP_DEFINE_TIME_DATA_H_
#include <chrono>
#include <mutex>

template <typename T>
class ThreadSafeTimed
{
 public:
    ThreadSafeTimed();
    void reset(const T& tdata,double timeout=0.1);
    bool timeout();
    T get();
    T& operator()();

 protected:
    T data_;
    std::chrono::steady_clock::time_point tp_;
    std::mutex mutex_;
    double timeout_;

};

template <typename T>
ThreadSafeTimed<T>::ThreadSafeTimed()
{
  timeout_=0.1;
}

template <typename T>
void ThreadSafeTimed<T>::reset(const T& tdata,double timeout)
{
  std::lock_guard<std::mutex> lck (mutex_);
  data_=tdata;
  timeout_=timeout;
  tp_=std::chrono::steady_clock::now();
}

template <typename T>
bool ThreadSafeTimed<T>::timeout()
{
  auto now=std::chrono::steady_clock::now();
  auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now - tp_);
  double time = double(duration.count()) * std::chrono::microseconds::period::num / std::chrono::microseconds::period::den;
  return time>timeout_;
}

template <typename T>
T ThreadSafeTimed<T>::get()
{
  std::lock_guard<std::mutex> lck (mutex_);
  return data_;
}

template <typename T>
T& ThreadSafeTimed<T>::operator()()
{
  std::lock_guard<std::mutex> lck (mutex_);
  return data_;
}


#endif //_CPP_DEFINE_TIME_DATA_H_
