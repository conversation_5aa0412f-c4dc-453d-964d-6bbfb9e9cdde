<?xml version="1.0"?>

<launch>
  <arg name="rviz" default="false" />

  <!--node pkg="map_server" name="map_server" type="map_server" args="$(find terrain_analysis)/maps/map.yaml"/-->

  <node name="terrain_analysis" pkg="terrain_analysis" type="terrain_analysis" output="screen">
    <rosparam command="load" file="$(find terrain_analysis)/config/patchworkpp.yaml" />
    <rosparam command="load" file="$(find terrain_analysis)/config/dynamic_detector.yaml" />
  </node>
  
  <group if="$(arg rviz)">
    <node name="rviz" pkg="rviz" type="rviz" args="-d $(find terrain_analysis)/launch/display.rviz" />
  </group>
</launch>
     
   